using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Pages
{
    public class RegisterModel : PageModel
    {
        private readonly AppDbContext _dbContext;
        private readonly HttpContextHelper _httpContextHelper;
        private readonly ILogger<RegisterModel> _logger;
        private readonly SampleDataService _sampleDataService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ReferralService _referralService;
        private readonly EmailVerificationService _emailVerificationService;

        public RegisterModel(
            AppDbContext dbContext,
            HttpContextHelper httpContextHelper,
            ILogger<RegisterModel> logger,
            SampleDataService sampleDataService,
            IStringLocalizer<SharedResource> localizer,
            ReferralService referralService,
            EmailVerificationService emailVerificationService)
        {
            _dbContext = dbContext;
            _httpContextHelper = httpContextHelper;
            _logger = logger;
            _sampleDataService = sampleDataService;
            _localizer = localizer;
            _referralService = referralService;
            _emailVerificationService = emailVerificationService;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public class InputModel
        {
            [Required(ErrorMessage = "Identity Number is required")]
            [Display(Name = "Identity Number")]
            public string IdentityNumber { get; set; } = string.Empty;

            [Required(ErrorMessage = "First Name is required")]
            [Display(Name = "First Name")]
            public string Name { get; set; } = string.Empty;

            [Required(ErrorMessage = "Last Name is required")]
            [Display(Name = "Last Name")]
            public string Surname { get; set; } = string.Empty;

            [Required(ErrorMessage = "Phone Number is required")]
            [Phone]
            [Display(Name = "Phone Number")]
            public string PhoneNumber { get; set; } = string.Empty;

            [Required(ErrorMessage = "Email is required")]
            [EmailAddress(ErrorMessage = "Enter a valid email")]
            [Display(Name = "Email Address")]
            public string Email { get; set; } = string.Empty;

            [Required(ErrorMessage = "Birth Date is required")]
            [Display(Name = "Birth Date")]
            public DateTime BirthDate { get; set; }

            [Required(ErrorMessage = "Password is required")]
            [StringLength(100, ErrorMessage = "Password must be at least {2} characters long.", MinimumLength = 6)]
            [DataType(DataType.Password)]
            [Display(Name = "Password")]
            public string Password { get; set; } = string.Empty;

            [Display(Name = "Referral Code")]
            public string? ReferralCode { get; set; }
        }

        public void OnGet(string? referenceCode = null)
        {
            // If referenceCode is provided in the query string, set it in the Input model
            if (!string.IsNullOrWhiteSpace(referenceCode))
            {
                Input.ReferralCode = referenceCode;
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                // Check if user already exists
                if (_dbContext.Users.Any(u => u.Email == Input.Email))
                {
                    ModelState.AddModelError(string.Empty, _localizer["User already exists"].Value);
                    return Page();
                }
                // Check if user already exists
                if (_dbContext.Users.Any(u => u.IdentityNumber == Input.IdentityNumber))
                {
                    ModelState.AddModelError(string.Empty, _localizer["Identity already exists"].Value);
                    return Page();
                }

                // Check referral code if provided
                User? referrer = null;
                if (!string.IsNullOrWhiteSpace(Input.ReferralCode))
                {
                    referrer = await _referralService.GetUserByReferralCodeAsync(Input.ReferralCode);
                    if (referrer == null)
                    {
                        ModelState.AddModelError("Input.ReferralCode", _localizer["Invalid referral code"].Value);
                        return Page();
                    }

                    // Check if the referrer has reached their invite limit
                    if (await _referralService.HasReachedInviteLimitAsync(referrer.UserId))
                    {
                        ModelState.AddModelError("Input.ReferralCode", _localizer["This referral code has reached its invite limit. Please use a different code."].Value);
                        return Page();
                    }
                }

                // Generate a unique referral code for the new user
                string referralCode = await _referralService.GenerateUniqueReferralCodeAsync();

                // Convert local date to UTC for storage
                var birthDate = DateTimeFormatHelper.ConvertToUtc(Input.BirthDate);

                // Create new user
                var user = new User
                {
                    IdentityNumber = Input.IdentityNumber,
                    Name = Input.Name,
                    Surname = Input.Surname,
                    PhoneNumber = Input.PhoneNumber,
                    Email = Input.Email,
                    BirthDate = birthDate,
                    PasswordHash = HashHelper.getHash(Input.Password),
                    IsActive = 1,
                    CrDate = DateTime.UtcNow,
                    ReferralCode = referralCode,
                    ReferrerId = referrer?.UserId,
                    UserRoleRelations = [new UserRoleRelation { RoleId = (int)Roller.User, IsActive = 1 }] // Default role
                };

                _dbContext.Users.Add(user);
                await _dbContext.SaveChangesAsync();

                //// Generate sample data for the user if in debug mode and on localhost
                //if (_sampleDataService.IsDebugAndLocalhost())
                //{
                //    await _sampleDataService.GenerateSampleDataForUserAsync(user.UserId);
                //    _logger.LogInformation("Generated sample data for user {UserId}", user.UserId);
                //}

                // Send email verification (don't block registration if email fails)
                try
                {
                    var emailSent = await _emailVerificationService.SendVerificationEmailAsync(
                        user.UserId,
                        user.Email,
                        $"{user.Name} {user.Surname}");

                    if (emailSent)
                    {
                        _logger.LogInformation("Email verification sent successfully to user {UserId}", user.UserId);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to send email verification to user {UserId}, but registration continues", user.UserId);
                    }
                }
                catch (Exception emailEx)
                {
                    _logger.LogError(emailEx, "Error sending email verification to user {UserId}, but registration continues", user.UserId);
                }

                // Sign in the user
                await _httpContextHelper.StartUserSession(
                    user: user,
                    returnUrl: "/",
                    AuthConstants.UserAuthenticationScheme
                );

                // Redirect new users to MyAccount area
                return RedirectToPage("/Dashboard", new { area = "MyAccount" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration");
                ModelState.AddModelError(string.Empty, "Kayıt işlemi sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.");
                return Page();
            }
        }
    }
}
